# CORS问题解决方案

## 问题描述

你遇到的错误是典型的CORS（跨域资源共享）问题：

```
Access to fetch at 'https://mail.xoxome.online/api/auth/login' from origin 'https://login.augmentcode.com' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

**问题原因：**
- 前端运行在：`https://login.augmentcode.com`
- API服务器在：`https://mail.xoxome.online`
- 浏览器的同源策略阻止了跨域请求
- 目标服务器没有设置正确的CORS头部

## 解决方案

### 方案1：使用Tampermonkey脚本（推荐）

我已经修改了你的 `augment.js` 文件，现在它会：

1. **优先使用 `GM_xmlhttpRequest`**：在Tampermonkey环境中，这个API可以绕过CORS限制
2. **降级到普通fetch**：在普通浏览器环境中使用fetch作为备用

**使用步骤：**
1. 安装Tampermonkey浏览器扩展
2. 将修改后的 `augment.js` 作为用户脚本安装
3. 脚本会自动在 `https://*.augmentcode.com/*` 页面上运行

### 方案2：使用CORS代理服务

如果无法使用Tampermonkey，可以使用 `cors-proxy-solution.js` 文件中的代理方案：

```javascript
// 加载代理解决方案
const api = new XoxoMeAPIWithProxy({
    username: 'halo',
    password: '12345678aka'
});

// 使用示例
try {
    await api.login();
    const emailInfo = await api.createEmail();
    console.log('创建的邮箱:', emailInfo.email);
    
    const code = await api.waitForVerificationCode(emailInfo.sessionId);
    console.log('验证码:', code);
} catch (error) {
    console.error('操作失败:', error);
}
```

### 方案3：后端代理（最佳长期解决方案）

如果你有后端服务器控制权，可以：

1. **在后端添加代理接口**：
```javascript
// 后端代理示例 (Node.js/Express)
app.post('/api/proxy/xoxome/login', async (req, res) => {
    try {
        const response = await fetch('https://mail.xoxome.online/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(req.body)
        });
        
        const data = await response.json();
        res.json(data);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});
```

2. **前端调用后端代理**：
```javascript
const response = await fetch('/api/proxy/xoxome/login', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        username: 'halo',
        password: '12345678aka'
    })
});
```

## 当前修改说明

我已经修改了你的 `augment.js` 文件中的以下方法：

1. **`login()`** - 登录方法
2. **`getSuffixes()`** - 获取域名后缀
3. **`createEmail()`** - 创建邮箱
4. **`getEmails()`** - 获取邮件列表

每个方法现在都会：
- 首先检查是否在Tampermonkey环境中（`typeof GM_xmlhttpRequest !== 'undefined'`）
- 如果是，使用 `GM_xmlhttpRequest` 发送请求（绕过CORS）
- 如果不是，降级使用普通的 `fetch`（可能遇到CORS问题）

## 测试步骤

1. **安装Tampermonkey**：
   - Chrome: 从Chrome Web Store安装
   - Firefox: 从Firefox Add-ons安装

2. **安装脚本**：
   - 打开Tampermonkey管理面板
   - 点击"创建新脚本"
   - 复制修改后的 `augment.js` 内容
   - 保存脚本

3. **测试运行**：
   - 访问 `https://login.augmentcode.com`
   - 脚本应该自动运行
   - 查看控制台日志确认是否使用了 `GM_xmlhttpRequest`

## 故障排除

如果仍然遇到CORS问题：

1. **确认Tampermonkey已启用**
2. **检查脚本权限设置**：确保 `@connect mail.xoxome.online` 已配置
3. **尝试代理方案**：使用 `cors-proxy-solution.js`
4. **联系API提供方**：请求他们添加CORS支持

## 注意事项

- 公共CORS代理可能不稳定，仅用于测试
- 生产环境建议使用后端代理方案
- Tampermonkey方案最适合个人使用和测试
