# XoxoMe 邮箱服务 API 文档

## 概述

XoxoMe 是一个临时邮箱服务，提供 REST API 接口用于创建临时邮箱和接收邮件。

- **服务地址**: https://mail.xoxome.online
- **认证方式**: JWT <PERSON> (Cookie + Authorization Header)
- **响应格式**: JSON
- **默认域名**: 7758.baby

## 认证

所有 API 请求都需要包含 JWT Token：

**方式一：Cookie**
```
Cookie: token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImJmc2RmamtibmoiLCJpYXQiOjE3NTQzMDA5NzIsImV4cCI6MTc1NDM4NzM3Mn0.cO9zN1EVVkatdH-KsB5VLCIRidwNH1jjmELuu2yExek
```

**方式二：Authorization Header**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImJmc2RmamtibmoiLCJpYXQiOjE3NTQzMDA5NzIsImV4cCI6MTc1NDM4NzM3Mn0.cO9zN1EVVkatdH-KsB5VLCIRidwNH1jjmELuu2yExek
```

## API 接口

### 1. 获取可用域名后缀

获取当前可用的邮箱域名后缀列表。

**请求**
```http
GET /api/email/suffixes
Cookie: token={jwt_token}
Accept: application/json
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
```

**响应**
```json
{
  "success": true,
  "data": [
    "7758.baby",
    "xoxome.online",
    "temp-mail.org"
  ]
}
```

### 2. 创建邮箱

创建一个新的临时邮箱地址。

**请求**
```http
POST /api/email/generate
Content-Type: application/json
Accept: application/json
Cookie: token={jwt_token}
Referer: https://mail.xoxome.online/dashboard
Origin: https://mail.xoxome.online

{
  "suffix": "7758.baby"
}
```

**参数说明**
- `suffix` (string, 必需): 邮箱后缀域名

**响应**
```json
{
  "success": true,
  "data": {
    "email": "<EMAIL>",
    "prefix": "randomstring123",
    "suffix": "7758.baby"
  },
  "message": "邮箱创建成功"
}
```

### 3. 获取邮件列表

通过 IMAP 获取指定邮箱的邮件列表。

**请求**
```http
GET /api/emails/imap-fetch?sessionId={session_id}
Accept: application/json
Authorization: Bearer {jwt_token}
Cookie: token={jwt_token}
Referer: https://mail.xoxome.online/dashboard
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
```

**参数说明**
- `sessionId` (string, 必需): 创建邮箱时生成的会话ID

**响应**
```json
{
  "success": true,
  "data": [
    {
      "from": "<EMAIL>",
      "to": "<EMAIL>",
      "subject": "验证您的邮箱地址",
      "text": "您的验证码是: 123456",
      "html": "<p>您的验证码是: <strong>123456</strong></p>",
      "date": "2024-01-01T12:05:00Z",
      "messageId": "<<EMAIL>>",
      "headers": {
        "message-id": "<<EMAIL>>",
        "date": "Mon, 01 Jan 2024 12:05:00 +0000",
        "from": "<EMAIL>",
        "to": "<EMAIL>",
        "subject": "验证您的邮箱地址"
      }
    }
  ]
}
```

## 使用示例

### JavaScript 示例

```javascript
const XOXOME_CONFIG = {
    apiBase: 'https://mail.xoxome.online',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    defaultSuffix: '7758.baby'
};

// 1. 获取可用域名后缀
async function getSuffixes() {
    const response = await fetch(`${XOXOME_CONFIG.apiBase}/api/email/suffixes`, {
        headers: {
            'Accept': 'application/json',
            'Cookie': `token=${XOXOME_CONFIG.token}`
        }
    });
    
    const data = await response.json();
    return data.success ? data.data : [];
}

// 2. 创建邮箱
async function createEmail() {
    const response = await fetch(`${XOXOME_CONFIG.apiBase}/api/email/generate`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Cookie': `token=${XOXOME_CONFIG.token}`,
            'Referer': 'https://mail.xoxome.online/dashboard',
            'Origin': 'https://mail.xoxome.online'
        },
        body: JSON.stringify({
            suffix: XOXOME_CONFIG.defaultSuffix
        })
    });
    
    const data = await response.json();
    if (data.success) {
        // 生成 sessionId 用于后续获取邮件
        const sessionId = `sync_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        return {
            email: data.data.email,
            sessionId: sessionId
        };
    }
    throw new Error(data.message || '创建邮箱失败');
}

// 3. 获取邮件
async function getEmails(sessionId) {
    const response = await fetch(
        `${XOXOME_CONFIG.apiBase}/api/emails/imap-fetch?sessionId=${sessionId}`,
        {
            headers: {
                'Accept': 'application/json',
                'Authorization': `Bearer ${XOXOME_CONFIG.token}`,
                'Cookie': `token=${XOXOME_CONFIG.token}`,
                'Referer': 'https://mail.xoxome.online/dashboard'
            }
        }
    );
    
    const data = await response.json();
    return data.success ? data.data : [];
}

// 4. 提取验证码
function extractVerificationCode(emailContent) {
    const patterns = [
        /验证码[：:\s]*(\d{4,8})/i,
        /verification code[：:\s]*(\d{4,8})/i,
        /code[：:\s]*(\d{4,8})/i,
        /(\d{6})/,  // 6位数字
        /(\d{4})/   // 4位数字
    ];
    
    for (const pattern of patterns) {
        const match = emailContent.match(pattern);
        if (match && match[1]) {
            return match[1];
        }
    }
    return null;
}
```

## 错误处理

**常见错误响应**
```json
{
  "success": false,
  "message": "Token无效或已过期"
}
```

**HTTP状态码**
- `200`: 请求成功
- `400`: 请求参数错误
- `401`: 认证失败
- `500`: 服务器内部错误

## 注意事项

1. **Token有效期**: JWT Token有时效性，过期后需要重新获取
2. **SessionId**: 创建邮箱后需要生成sessionId用于获取邮件
3. **请求头**: 需要设置正确的Referer和Origin
4. **邮件延迟**: 邮件可能有延迟，建议轮询获取
5. **域名变更**: 可用域名后缀可能会变更，建议先获取后缀列表
