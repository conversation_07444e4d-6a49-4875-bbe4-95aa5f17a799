/**
 * CORS代理解决方案
 * 当无法使用Tampermonkey的GM_xmlhttpRequest时的备用方案
 */

// 方案1: 使用公共CORS代理服务
const CORS_PROXIES = [
    'https://cors-anywhere.herokuapp.com/',
    'https://api.allorigins.win/raw?url=',
    'https://corsproxy.io/?'
];

/**
 * 使用CORS代理发送请求
 */
async function fetchWithProxy(url, options = {}) {
    const errors = [];
    
    for (const proxy of CORS_PROXIES) {
        try {
            const proxyUrl = proxy + encodeURIComponent(url);
            console.log(`尝试使用代理: ${proxy}`);
            
            const response = await fetch(proxyUrl, {
                ...options,
                headers: {
                    ...options.headers,
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            if (response.ok) {
                return response;
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.warn(`代理 ${proxy} 失败:`, error.message);
            errors.push(`${proxy}: ${error.message}`);
        }
    }
    
    throw new Error(`所有CORS代理都失败了:\n${errors.join('\n')}`);
}

// 方案2: 修改XoxoMeAPI类使用代理
class XoxoMeAPIWithProxy {
    constructor(config = {}) {
        this.config = {
            apiBase: 'https://mail.xoxome.online',
            loginUrl: 'https://mail.xoxome.online/api/auth/login',
            username: config.username || 'halo',
            password: config.password || '12345678aka',
            ...config
        };

        this.token = null;
        this.user = null;
        this.availableSuffixes = null;
    }

    async login() {
        try {
            const response = await fetchWithProxy(this.config.loginUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: this.config.username,
                    password: this.config.password
                })
            });

            const result = await response.json();

            if (result.success) {
                this.token = result.token;
                this.user = result.user;
                console.log('XoxoMe 登录成功');
                return result;
            } else {
                throw new Error(result.message || '登录失败');
            }
        } catch (error) {
            console.error('XoxoMe 登录失败:', error);
            throw error;
        }
    }

    getHeaders() {
        const headers = {
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        };

        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
            headers['Cookie'] = `token=${this.token}`;
        }

        return headers;
    }

    async getSuffixes() {
        if (!this.token) {
            await this.login();
        }

        try {
            const response = await fetchWithProxy(`${this.config.apiBase}/api/email/suffixes`, {
                headers: this.getHeaders()
            });

            const data = await response.json();
            const suffixes = data.success ? data.data : [];
            this.availableSuffixes = suffixes;
            return suffixes;
        } catch (error) {
            console.error('获取域名后缀失败:', error);
            throw error;
        }
    }

    async createEmail(suffix = null) {
        if (!this.token) {
            await this.login();
        }

        if (!suffix) {
            if (!this.availableSuffixes || this.availableSuffixes.length === 0) {
                await this.getSuffixes();
            }
            const randomIndex = Math.floor(Math.random() * this.availableSuffixes.length);
            suffix = this.availableSuffixes[randomIndex];
        }

        try {
            const response = await fetchWithProxy(`${this.config.apiBase}/api/email/generate`, {
                method: 'POST',
                headers: {
                    ...this.getHeaders(),
                    'Content-Type': 'application/json',
                    'Referer': 'https://mail.xoxome.online/dashboard',
                    'Origin': 'https://mail.xoxome.online'
                },
                body: JSON.stringify({
                    suffix: suffix
                })
            });

            const data = await response.json();

            if (data.success) {
                const sessionId = `sync_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
                return {
                    email: data.data.email,
                    prefix: data.data.prefix,
                    suffix: data.data.suffix,
                    sessionId: sessionId
                };
            } else {
                throw new Error(data.message || '创建邮箱失败');
            }
        } catch (error) {
            console.error('创建邮箱失败:', error);
            throw error;
        }
    }

    async getEmails(sessionId) {
        if (!this.token) {
            await this.login();
        }

        try {
            const response = await fetchWithProxy(
                `${this.config.apiBase}/api/emails/imap-fetch?sessionId=${sessionId}`,
                {
                    headers: {
                        ...this.getHeaders(),
                        'Referer': 'https://mail.xoxome.online/dashboard'
                    }
                }
            );

            const data = await response.json();
            return data.success ? data.data : [];
        } catch (error) {
            console.error('获取邮件失败:', error);
            throw error;
        }
    }

    extractVerificationCode(emailContent) {
        const patterns = [
            /验证码[：:\s]*(\d{4,8})/i,
            /verification code[：:\s]*(\d{4,8})/i,
            /code[：:\s]*(\d{4,8})/i,
            /(\d{6})/,
            /(\d{4})/
        ];

        for (const pattern of patterns) {
            const match = emailContent.match(pattern);
            if (match && match[1]) {
                return match[1];
            }
        }
        return null;
    }

    async waitForVerificationCode(sessionId, maxRetries = 6, retryInterval = 5000) {
        for (let attempt = 0; attempt < maxRetries; attempt++) {
            console.log(`尝试获取验证码 (第 ${attempt + 1}/${maxRetries} 次)...`);

            try {
                const emails = await this.getEmails(sessionId);

                if (emails && emails.length > 0) {
                    const latestEmail = emails[0];
                    const emailContent = latestEmail.text || latestEmail.html || '';
                    const code = this.extractVerificationCode(emailContent);

                    if (code) {
                        console.log('成功获取验证码:', code);
                        return code;
                    }
                }

                if (attempt < maxRetries - 1) {
                    console.log(`未获取到验证码，${retryInterval/1000}秒后重试...`);
                    await new Promise(resolve => setTimeout(resolve, retryInterval));
                }
            } catch (error) {
                console.error('获取验证码出错:', error);
                if (attempt < maxRetries - 1) {
                    await new Promise(resolve => setTimeout(resolve, retryInterval));
                }
            }
        }

        throw new Error(`经过 ${maxRetries} 次尝试后仍未获取到验证码`);
    }
}

// 导出供使用
if (typeof window !== 'undefined') {
    window.XoxoMeAPIWithProxy = XoxoMeAPIWithProxy;
    window.fetchWithProxy = fetchWithProxy;
}

// 使用示例
console.log('CORS代理解决方案已加载');
console.log('使用方法: const api = new XoxoMeAPIWithProxy(); await api.login();');
